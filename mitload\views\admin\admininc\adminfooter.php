 <footer class="main-footer">
    <div class="pull-right hidden-xs">
      <b>Version</b> 2.5.0
    </div>
    <strong>Copyright &copy; 2014-2019 <a href=""Flexisoftwarebd.com</a>.</strong> All rights
    reserved.
  </footer>

<script src="<?php echo base_url(); ?>plugins/jQuery/jquery-3.1.1.min.js"></script>
  
<script src="<?php echo base_url(); ?>assets/fload/js/jquery.min.js" type="text/javascript" ></script>

   <!-- <script src="<?php echo base_url(); ?>assets/fload/js/bootstrap.min.js" type="text/javascript" ></script>-->
    <script src="<?php echo base_url(); ?>assets/fload/js/datepicker.js" type="text/javascript" ></script>
    <script src="<?php echo base_url(); ?>assets/fload/js/ajax_req.js" type="text/javascript" ></script>
	<script src="<?php echo base_url(); ?>assets/fload/js/smscounter.min.js" type="text/javascript" ></script>
	<script src="<?php echo base_url(); ?>assets/fload/js/chosen.js" type="text/javascript" ></script>
	
    <script src="<?php echo base_url(); ?>assets/fload/js/fortune.js" type="text/javascript" ></script>
		<script src="<?php echo base_url(); ?>assets/fload/js/highcharts.js" type="text/javascript" ></script>
	<script src="<?php echo base_url(); ?>assets/fload/js/exporting.js" type="text/javascript" ></script>
	
  
<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->

<!-- jQuery 3.1.1 --
<script src="<?php echo base_url(); ?>plugins/jQuery/jquery-3.1.1.min.js"></script>
<!-- jQuery UI 1.11.4 --
<script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button);
</script>


<!-- Bootstrap 3.3.7 -->
<script src="<?php echo base_url(); ?>bootstrap/js/bootstrap.min.js"></script>
<!-- Morris.js charts --
<script src="https://cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>-->

<script src="<?php echo base_url(); ?>plugins/morris/morris.min.js"></script>
<!-- Sparkline -->
<script src="<?php echo base_url(); ?>plugins/sparkline/jquery.sparkline.min.js"></script>
<!-- jvectormap -->
<script src="<?php echo base_url(); ?>plugins/jvectormap/jquery-jvectormap-1.2.2.min.js"></script>
<script src="<?php echo base_url(); ?>plugins/jvectormap/jquery-jvectormap-world-mill-en.js"></script>
<!-- jQuery Knob Chart -->
<script src="<?php echo base_url(); ?>plugins/knob/jquery.knob.js"></script>
<!-- daterangepicker -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment.min.js"></script>
<script src="<?php echo base_url(); ?>plugins/daterangepicker/daterangepicker.js"></script>
<!-- datepicker -->
<script src="<?php echo base_url(); ?>plugins/datepicker/bootstrap-datepicker.js"></script>
<!-- Bootstrap WYSIHTML5 -->
<script src="<?php echo base_url(); ?>plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.all.min.js"></script>
<!-- Slimscroll -->
<script src="<?php echo base_url(); ?>plugins/slimScroll/jquery.slimscroll.min.js"></script>
<!-- FastClick -->
<script src="<?php echo base_url(); ?>plugins/fastclick/fastclick.js"></script>
<!-- AdminLTE App -->
<script src="<?php echo base_url(); ?>dist/js/adminlte.min.js"></script>
<!-- AdminLTE dashboard demo (This is only for demo purposes) -->
<script src="<?php echo base_url(); ?>dist/js/pages/dashboard.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="<?php echo base_url(); ?>dist/js/demo.js"></script>

<!-- Quick Balance Add Modal -->
<div class="modal fade" id="quickBalanceModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Quick Balance Add</h4>
            </div>
            <div class="modal-body">
                <form id="quickBalanceForm">
                    <div class="form-group">
                        <label>Search Reseller</label>
                        <input type="text" id="resellerSearch" class="form-control" placeholder="Type username or phone number">
                        <div id="resellerResults" class="list-group" style="max-height: 200px; overflow-y: auto; margin-top: 5px;"></div>
                    </div>
                    <div id="selectedResellerInfo" style="display: none;">
                        <div class="alert alert-info">
                            <strong>Selected Reseller:</strong> <span id="selectedResellerName"></span><br>
                            <strong>Current Balance:</strong> <span id="selectedResellerBalance"></span> TK
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Balance Type</label>
                        <select id="balanceType" class="form-control">
                            <option value="main">Main Balance</option>
                            <option value="bank">Bank Balance</option>
                            <option value="drive">Drive Balance</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Amount</label>
                        <input type="number" id="balanceAmount" class="form-control" step="0.01" min="1" placeholder="Enter amount">
                    </div>
                    <div class="form-group">
                        <label>Description</label>
                        <textarea id="balanceDescription" class="form-control" rows="2" placeholder="Reason for balance addition"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Admin PIN</label>
                        <input type="password" id="adminPin" class="form-control" placeholder="Enter your admin PIN">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="processQuickBalance()">Add Balance</button>
            </div>
        </div>
    </div>
</div>

<script>
var selectedResellerId = null;

function openDirectBalanceModal() {
    $('#quickBalanceModal').modal('show');
    $('#resellerSearch').focus();
}

// Search resellers
$('#resellerSearch').on('input', function() {
    var query = $(this).val().trim();
    if (query.length >= 2) {
        $.ajax({
            url: '<?php echo base_url(); ?>admin/searchResellers',
            method: 'POST',
            data: { query: query },
            dataType: 'json',
            success: function(data) {
                var html = '';
                if (data.length > 0) {
                    data.forEach(function(reseller) {
                        html += '<a href="javascript:void(0)" class="list-group-item" onclick="selectReseller(' + reseller.id + ', \'' + reseller.username + '\', \'' + reseller.name + '\', ' + reseller.balance + ')">';
                        html += '<strong>' + reseller.username + '</strong> - ' + reseller.name + '<br>';
                        html += '<small>Balance: ' + reseller.balance + ' TK | Phone: ' + reseller.phone + '</small>';
                        html += '</a>';
                    });
                } else {
                    html = '<div class="list-group-item">No resellers found</div>';
                }
                $('#resellerResults').html(html);
            }
        });
    } else {
        $('#resellerResults').empty();
    }
});

function selectReseller(id, username, name, balance) {
    selectedResellerId = id;
    $('#selectedResellerName').text(username + ' (' + name + ')');
    $('#selectedResellerBalance').text(balance);
    $('#selectedResellerInfo').show();
    $('#resellerResults').empty();
    $('#resellerSearch').val(username);
}

function processQuickBalance() {
    if (!selectedResellerId) {
        alert('Please select a reseller first');
        return;
    }

    var amount = $('#balanceAmount').val();
    var description = $('#balanceDescription').val();
    var source = $('#balanceType').val();
    var pin = $('#adminPin').val();

    if (!amount || amount < 1) {
        alert('Please enter a valid amount');
        return;
    }

    if (!description.trim()) {
        alert('Please enter description');
        return;
    }

    if (!pin.trim()) {
        alert('Please enter admin PIN');
        return;
    }

    if (confirm('Add ' + amount + ' TK to ' + source + ' balance?\n\nThis action cannot be undone!')) {
        $.ajax({
            url: '<?php echo base_url(); ?>admin/processQuickBalance',
            method: 'POST',
            data: {
                reseller_id: selectedResellerId,
                amount: amount,
                description: description,
                source: source,
                pincode: pin
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('Balance added successfully!');
                    $('#quickBalanceModal').modal('hide');
                    // Reset form
                    $('#quickBalanceForm')[0].reset();
                    $('#selectedResellerInfo').hide();
                    selectedResellerId = null;
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            }
        });
    }
}

// Reset modal when closed
$('#quickBalanceModal').on('hidden.bs.modal', function() {
    $('#quickBalanceForm')[0].reset();
    $('#selectedResellerInfo').hide();
    $('#resellerResults').empty();
    selectedResellerId = null;
});
</script>



</body>
</html>
