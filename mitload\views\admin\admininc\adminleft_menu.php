  <!-- Left side column. contains the logo and sidebar -->
  <aside class="main-sidebar" style="background-color: #D397DD">
    <!-- sidebar: style can be found in sidebar.less -->
    <section class="sidebar">
      <!-- Sidebar user panel -->
      <div class="user-panel">
        <div class="pull-left image">
          <img src="<?php echo "".base_url()."$system_logo"; ?>" class="img-circle" alt="User Image">
        </div>
        <div class="pull-left info">
          <p><?= $uname  ?> </p>
           <?php echo number_format($totalsim, 2); ?>
        </div>
      </div>
     



      <!-- sidebar menu: : style can be found in sidebar.less -->
      <ul style="background-color: #D397DD" class="sidebar-menu" data-widget="tree">
        <li style="background-color: #D397DD" class="header"><?php echo translate('MAIN_NAVIGATION'); ?> </li>
       
       <li style="background-color: #D397DD" class="<?php echo (isset($page_name) && ($page_name=='dashboard')) ? 'active' : '';?>">
          <a href="<?php echo base_url(); ?>admin">
            <i class="fa fa-desktop"></i> <span><?php echo translate('dashboard'); ?></span>
            

          </a>
        </li>
 <li style="background-color: #D397DD" class="<?php echo (isset($page_name) && ($page_name=='backup')) ? 'active' : '';?>">
          <a href="<?php echo base_url(); ?>admin/backup">
            <i class="fa fa-save"></i> <span><?php echo translate('backup'); ?></span>
            

          </a>
        </li>

      <?php if($page_request_view==1) {  ?>
        <li style="background-color: #D397DD" class="<?php echo (isset($page_name) && ($page_name=='pending')) ? 'active' : '';?>">
          <a href="<?php echo base_url(); ?>admin/pending">
            <i class="fa fa-clock-o"></i> <span><?php echo translate('pendding_request'); ?>
            <?php 
         
  
    $query = $this->db->query("SELECT * FROM sendflexi WHERE (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4')");


if($query->num_rows() > 0 ) { 
echo "<small class='label bg-red'>$query->num_rows </small>";}

?>

          </a>
        </li>
	  <?php } if($page_request_details=="1") { ?>
	
	
        <li style="background-color: #D397DD" class="treeview <?php echo (isset($page_name) && ($page_name=='history_list')) ? 'active' : '';?>">
          <a href="#">
            <i class="fa fa-hdd-o"></i>
            <span><?php echo translate('recharge_history'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">

           <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/history/all"><i class="fa fa-arrow-right"></i> <?= translate('all_history'); ?></a></li>

          <?php 
        
        $sql="SELECT * from module where status='1' and id>=0 and 6<=id  order by sorder asc"; 
        
        $query = $this->db->query($sql);

          foreach ($query->result() as $row)
          {
              $titleop=$row->title; 
              $serviceid=$row->serviceid; 
               $portid=$row->id; 
                      ?>

            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/history/<?php echo $titleop ?>"><i class="fa fa-arrow-right"></i> <?php echo $titleop; ?></a></li>
            
              <?php  }  ?>       

          </ul>
        </li>
		
		<?php } if($page_sms_view=="1") {  ?>

        <!-- sms inbox-->

         <li style="background-color: #D397DD" class="treeview <?php echo (isset($page_name) && ($page_name=='sms_inbox')) ? 'active' : '';?>">
          <a href="#">
            <i class="fa fa-envelope-o"></i>
            <span><?php echo translate('message_inbox'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">

             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/smsInbox/all"><i class="fa fa-arrow-right"></i> <?= translate('all_sms'); ?></a></li>

          <?php 
        
        $sql="SELECT * from module where status='1' and id>=0 and 6<=id  order by sorder asc"; 
        
        $query = $this->db->query($sql);

          foreach ($query->result() as $row)
          {
              $titleop=$row->title; 
              $serviceid=$row->serviceid; 
               $portid=$row->id; 
                      ?>

            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/smsInbox/<?php echo $titleop ?>"><i class="fa fa-arrow-right"></i> <?php echo $titleop; ?></a></li>
            
              <?php  }  ?>       

          </ul>
        </li>
		
		<?php } if($page_internet_per==1) { ?>


            <li style="background-color: #D397DD" class="treeview <?php echo (isset($page_name) && ($page_name=='offer_list')) ? 'active' : '';?>">
          <a href="#">
            <i class="fa fa-leaf"></i>
            <span><?php echo translate('offer_settings'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">

           <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/offer/oparetor"><i class="fa fa-arrow-right"></i> <?= translate('oparetor_add'); ?></a></li>

             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/d_operator/"><i class="fa fa-arrow-right"></i> <?= translate('Regular Package'); ?></a></li>
             
              <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/b_operator/"><i class="fa fa-arrow-right"></i> <?= translate('Drive package'); ?></a></li>

       
          </ul>
        </li>
		<?php } ?>
		
		<?php 
	$servicemenu_per=$this->mit->serviceMneuper(4);
	if($servicemenu_per==1) { ?>
	
	 <li style="background-color: #D397DD" class="treeview <?php echo (isset($page_name) && ($page_name=='cardHistory')) ? 'active' : '';?>">
          <a href="#">
            <i class="fa fa-credit-card"></i>
            <span><?php echo translate('card_management'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		 <?php if($page_prepaid_history=="1") { ?>
           <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/cardHistory"><i class="fa fa-arrow-right"></i> <?= translate('card_history'); ?></a></li>

		 <?php } if($page_prepaid_add=="1") { ?>
             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/card/add"><i class="fa fa-arrow-right"></i> <?= translate('card_add'); ?></a></li>
			 
		   <?php } if($page_prepaid_manage=="1") { ?>
			  <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/card"><i class="fa fa-arrow-right"></i> <?= translate('card_management'); ?></a></li>
			  
		   <?php } ?>

       
          </ul>
        </li>
		
	<?php } ?>
	
	<?php 
	$servicemenu_per=$this->mit->serviceMneuper(8);
	if($servicemenu_per==1) { ?>
	
	 <li style="background-color: #D397DD" class="treeview <?php echo (isset($page_name) && ($page_name=='cardHistory')) ? 'active' : '';?>">
          <a href="#">
            <i class="fa fa-money"></i>
            <span><?php echo translate('billpay'); ?><?php 
         $querybilpay = $this->db->query("SELECT * FROM bill_pay WHERE status='0'");
			if($querybilpay->num_rows() > 0 ) { 
			echo "<small class='label pull-right bg-red'>$querybilpay->num_rows </small>";}
			?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		  
		    <?php if($page_request_view==1) {  ?> 

           <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/billpaypending"><i class="fa fa-arrow-right"></i> <?= translate('pending_request'); ?>
		   <?php 
         $querybilpay = $this->db->query("SELECT * FROM bill_pay WHERE status='0'");
			if($querybilpay->num_rows() > 0 ) { 
			echo "<small class='label pull-right bg-red'>$querybilpay->num_rows </small>";}
			?>
		   </a></li>   <?php } if($page_billpay_history=="1") { ?>

             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/billpayhistory"><i class="fa fa-arrow-right"></i> <?= translate('billpay_history'); ?></a></li>
			 
			  <?php } if($page_billpay_settings=="1") { ?>
			  
			  <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/billpaymng"><i class="fa fa-arrow-right"></i> <?= translate('billpay_settings'); ?></a></li>
			  <?php } ?>
       
          </ul>
        </li>
		
	<?php } ?>
	
	<?php 
	$servicemenu_per=$this->mit->serviceMneuper(32);
	if($servicemenu_per==1) { ?>
	
	 <li style="background-color: #D397DD" class="treeview <?php echo (isset($page_name) && ($page_name=='cardHistory')) ? 'active' : '';?>">
          <a href="#">
            <i class="fa fa-bank"></i>
            <span><?php echo translate('banking'); ?> <?php 
         
  
    $querybankpn = $this->db->query("SELECT * FROM bank_transfer WHERE  status='0'");


if($querybankpn->num_rows() > 0 ) { 
echo "<small class='label pull-right bg-red'>$querybankpn->num_rows </small>";}

?></span>
			
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
			
			 <?php if($page_request_view==1) {  ?>
           <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/bankpending"><i class="fa fa-arrow-right"></i> <?= translate('pending_request'); ?>
		   <?php 
         
  
    $querybankpn = $this->db->query("SELECT * FROM bank_transfer WHERE  status='0'");


if($querybankpn->num_rows() > 0 ) { 
echo "<small class='label pull-right bg-red'>$querybankpn->num_rows </small>";}

?>
</a></li>
	<?php } if($page_bank_history=="1") { ?>
             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/bankhistory"><i class="fa fa-arrow-right"></i> <?= translate('bank_history'); ?></a></li>
			 
			<?php } if($page_bank_settings=="1") { ?>
			 
			  <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/bankset"><i class="fa fa-arrow-right"></i> <?= translate('bank_settings'); ?></a></li>
			  
			 <?php } ?>

       
          </ul>
        </li>
		
	<?php } ?>
	
	 <?php if($page_reseller_manage==1) {  ?>

         <li style="background-color: #D397DD" class="<?php echo (isset($page_name) && ($page_name=='reseller_list')) ? 'active' : '';?>">
          <a href="<?php echo base_url(); ?>admin/resellers/subadmin">
            <i class="fa fa-user"></i> <span> <?= translate('sub_admin'); ?></span>
            

          </a>
        </li>


 
        <!-- reseller list -->

         <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-user"></i>
            <span><?php echo translate('resellers'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">

           <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/resellers/all"><i class="fa fa-arrow-right"></i> Reseller All</a></li>

          <?php

     for ($x = 5; $x >= 1; $x--) {
       // arsort($x);
		 $memberlevel="reseller$x";
		 	$newlevel = $this->db->get_where('level_list',array('name' =>$memberlevel))->row()->real_name;


               ?>

            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/resellers/<?php echo $x ?>"><i class="fa fa-arrow-right"></i> <?php echo $newlevel; ?></a></li>
            
              <?php  }  ?>      

          </ul>
        </li>
	 <?php } ?>

        <!-- Balance Management Section -->
        <?php if($page_reseller_manage==1) {  ?>
        <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-money"></i>
            <span>Balance Management</span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/balance"><i class="fa fa-bar-chart"></i> Balance Reports</a></li>
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/payments"><i class="fa fa-history"></i> Payment History</a></li>
            <li style="background-color: #D397DD" class="header" style="color: #fff; font-weight: bold; padding: 5px 15px;">Direct Balance Tools</li>
            <li style="background-color: #D397DD"><a href="javascript:void(0)" onclick="openDirectBalanceModal()"><i class="fa fa-plus-circle text-success"></i> Quick Balance Add</a></li>
          </ul>
        </li>
        <?php } ?>




          <?php if($page_payment_history==1) {  ?>
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/payments">
<i class="fa fa-table"></i> <span><?php echo translate('payment_history'); ?></span></a></li>
            
		  <?php } if($page_payment_history==1) { ?>
        <li style="background-color: #D397DD" class="treeview ">
          <a href="#">
            <i class="fa fa-calendar"></i> <span><?php echo translate('reports'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/recharge"><i class="fa fa-arrow-right"></i><?php echo translate('receive_reports'); ?></a></li>

            <!--<li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/recharge"><i class="fa fa-arrow-right"></i><?php echo translate('downline_user'); ?></a></li>-->

            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/balance"><i class="fa fa-arrow-right"></i><?php echo translate('balance_reports'); ?></a></li>

             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/opreports"><i class="fa fa-arrow-right"></i><?php echo translate('oparetor_reports'); ?></a></li>

              <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/dailyreports"><i class="fa fa-arrow-right"></i><?php echo translate('daily_reports'); ?></a></li>

               <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/totalUse"><i class="fa fa-arrow-right"></i><?php echo translate('total_usage'); ?></a></li>

                <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/Transaction"><i class="fa fa-arrow-right"></i><?php echo translate('transaction '); ?></a></li>

 <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/Transactioninfo"><i class="fa fa-arrow-right"></i><?php echo translate('Trnx ID '); ?></a></li>
				<li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/route_report"><i class="fa fa-arrow-right"></i><?php echo translate('sales_reports'); ?></a></li>

        
          </ul>
        </li>
		
		  <?php } ?>


        <!-- admin setting -->

        <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-wrench"></i> <span><?php echo translate('administration'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		   <?php if($page_server_module==1) {  ?>
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/serviceModual"><i class="fa fa-arrow-right"></i><?php echo translate('service_modual'); ?></a></li>
		   <?php } if($page_server_rate==1) { ?>
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/rateModual"><i class="fa fa-arrow-right"></i><?php echo translate('rate_modual'); ?></a></li>
			
			<li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/deposit"><i class="fa fa-arrow-right"></i><?php echo translate('Deposit deposit'); ?></a></li>
			 <?php } if($page_server_api==1) { ?>
			 
			  <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/modemlist"><i class="fa fa-arrow-right"></i><?php echo translate('modem_list'); ?></a></li>
  <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/modemdevice"><i class="fa fa-arrow-right"></i><?php echo translate('modem_device'); ?></a></li>
<li><a href="<?php echo base_url(); ?>admin/block_list"><i class="fa fa-arrow-right"></i><?php echo translate('recharge_block_list'); ?></a></li>
 
             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/apiset"><i class="fa fa-arrow-right"></i><?php echo translate('api_settings'); ?></a></li>
             
              <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/payment_gatway_list"><i class="fa fa-arrow-right"></i><?php echo translate('payment_gateway'); ?></a></li>
			 
			  <?php } if($page_server_security==1) { ?>

               <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/security"><i class="fa fa-arrow-right"></i><?php echo translate('security_settings'); ?></a></li>
			   
			   
			 <?php } if($page_server_delete_res==1) { ?>

  <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/delAcc"><i class="fa fa-arrow-right"></i><?php echo translate('deleted_accounts'); ?></a></li>
  
			 <?php } ?>


          </ul>
        </li>

        <!-- tools -->


         <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-cog"></i> <span><?php echo translate('tools'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		  
		   <?php if($page_tools_branding==1) { ?>
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/brand"><i class="fa fa-arrow-right"></i><?php echo translate('branding'); ?></a></li>
			
			
			
		   <?php } if($page_device_logs==1) { ?>
			 
			 
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/deviceLogs"><i class="fa fa-arrow-right"></i><?php echo translate('device_logs'); ?></a></li>
			
			 <?php } if($page_tools_latest_update==1) { ?>

            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/notice_update"><i class="fa fa-arrow-right"></i><?php echo translate('reseller_notice'); ?></a></li>
			
			 <?php } if($page_tools_notice==1) { ?>

             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/loginotice"><i class="fa fa-arrow-right"></i><?php echo translate('login_notice'); ?></a></li>

			 
             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/slides"><i class="fa fa-arrow-right"></i>Slides</a></li>
             
	
 
           
			 <?php } ?>

          </ul>
        </li>
		
		
		<li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-globe"></i> <span><?php echo translate('global'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		  
		  <?php  if($page_country_per==1) { ?>
		  
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/country"><i class="fa fa-arrow-right"></i> <?php echo translate('country'); ?></a></li>
			
			<?php } if($page_Oparetor_per==1) { ?>
			 
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/oparetor"><i class="fa fa-arrow-right"></i> <?php echo translate('oparetor'); ?></a></li>
			<?php } ?>
        
           

          </ul>
        </li>
       
        
        <?php  if($page_profile==1) {  ?>
        <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-graduation-cap"></i> <span><?php echo translate('admin_account'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/profile"><i class="fa fa-arrow-right"></i> <?php echo translate('my_profile'); ?></a></li>
			 <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/set2step"><i class="fa fa-arrow-right"></i> <?php echo translate('google otp'); ?></a></li>
			 
			 	 <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/m2otp"><i class="fa fa-arrow-right"></i> <?php echo translate('Email/Mobile otp'); ?></a></li>
			 
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/manageuser"><i class="fa fa-arrow-right"></i> <?php echo translate('manage_admin_user'); ?></a></li>
           
           
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/pin"><i class="fa fa-arrow-right"></i> <?php echo translate('change_pin'); ?></a></li>
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/password"><i class="fa fa-arrow-right"></i> <?php echo translate('change_password'); ?></a></li>
           

          </ul>
        </li>
        


        <!-- store -->


         <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-cog"></i> <span><?php echo translate('store'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		  
			<li style="background-color: #D397DD">
                 <a href="<?php echo base_url(); ?>admin/storedata"><i class="fa fa-arrow-right"></i>store data</a>
            </li>
            
        	<li style="background-color: #D397DD">
                 <a href="<?php echo base_url(); ?>admin/sell"><i class="fa fa-arrow-right"></i>Store Sell</a>
            </li>  		  



          </ul>
        </li>




        <!-- Ecommerce -->


         <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-cog"></i> <span><?php echo translate('ecommerce'); ?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		  

        	<li style="background-color: #D397DD">
                 <a href="<?php echo base_url(); ?>admin/tbl_category"><i class="fa fa-arrow-right"></i>category</a>
            </li>  		  

        	<li style="background-color: #D397DD">
                 <a href="<?php echo base_url(); ?>admin/tbl_order"><i class="fa fa-arrow-right"></i>tbl_order</a>
            </li>  	

 	<li style="background-color: #D397DD">
                 <a href="<?php echo base_url(); ?>admin/tbl_product"><i class="fa fa-arrow-right"></i>tbl_product</a>
            </li>  	



          </ul>
        </li>
















            



        
        
        
		<?php } if($page_complain==1) { ?>

        <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/viewtickets"><i class="fa fa-book"></i> <span><?php echo translate('complain'); ?></span>
		<?php 
			  
    $querycom = $this->db->query("SELECT * FROM complain where status='0'");


if($querycom->num_rows() > 0 ) { 
echo "<small class='label bg-red'>$querycom->num_rows </small>";}

?>
</a></li>

          <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/trickets"><i class="fa fa-comments"></i> <span><?php echo translate('Support'); ?> <?php 
         
  
    $query = $this->db->query("SELECT * FROM tricket_main WHERE status='0'");


if($query->num_rows() > 0 ) { 
echo "<small class='label bg-red'>$query->num_rows </small>";}
                                     
?></span>
	</a></li>
          
         

        
        
        
	   <?php } ?>

        <!-- others -->
<li style="background-color: #D397DD" class="header"><?php echo translate('othres'); ?></li>

         <li style="background-color: #D397DD" class="treeview">
          <a href="#">
            <i class="fa fa-compass"></i> <span><?php echo translate('othres'); ?>&nbsp; &nbsp;
			 <?php 
			  
    $queryonline = $this->db->query("SELECT * FROM user_online");


if($queryonline->num_rows() > 0 ) { 
echo "<small class='label bg-green'>$queryonline->num_rows </small>";}

?></span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul style="background-color: #D397DD" class="treeview-menu">
		    <?php if($page_online_user==1) { ?>
		  
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/online"><i class="fa fa-arrow-right"></i><?php echo translate('online_users'); ?> 
			<?php 
			  
    $queryonline = $this->db->query("SELECT * FROM user_online");


if($queryonline->num_rows() > 0 ) { 
echo "<small class='label bg-green'>$queryonline->num_rows </small>";}

?>
</a></li>

			<?php } if($page_access_logs==1) {  ?>
			
			
			
            <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/access"><i class="fa fa-arrow-right"></i><?php echo translate('access_logs'); ?></a></li>
			
			<?php } if($page_access_logs==1) {  ?>

             <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/activity_logs"><i class="fa fa-arrow-right"></i><?php echo translate('activity_logs'); ?></a></li>
			 
			<?php } ?>

          </ul>
        </li>

        <li style="background-color: #D397DD" class="header"><?php echo translate('logout'); ?></li>
        <li style="background-color: #D397DD"><a href="<?php echo base_url(); ?>admin/logout"><i class="fa fa-sign-out"></i> <span><?php echo translate('logout'); ?></span></a></li>
       

      </ul>
    </section>
    <!-- /.sidebar -->
  </aside>