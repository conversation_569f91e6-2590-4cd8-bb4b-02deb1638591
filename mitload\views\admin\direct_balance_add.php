<div class="row">
    <div class="col-md-8 col-md-offset-2">
        <div class="white-box">
            <h3 class="box-title">Direct Balance Addition</h3>
            <p class="text-muted">Add balance directly to reseller account (No pending system)</p>
            
            <?php if($this->session->flashdata('error')): ?>
                <div class="alert alert-danger">
                    <?php echo $this->session->flashdata('error'); ?>
                </div>
            <?php endif; ?>
            
            <?php if($this->session->flashdata('success')): ?>
                <div class="alert alert-success">
                    <?php echo $this->session->flashdata('success'); ?>
                </div>
            <?php endif; ?>
            
            <?php 
            $reseller_info = $this->db->get_where('reseller', array('id' => $resid))->row();
            ?>
            
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4>Reseller Information</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Username:</strong> <?php echo $reseller_info->username; ?><br>
                            <strong>Name:</strong> <?php echo $reseller_info->name; ?><br>
                            <strong>Phone:</strong> <?php echo $reseller_info->phone; ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Main Balance:</strong> <?php echo number_format($reseller_info->balance, 2); ?> TK<br>
                            <strong>Bank Balance:</strong> <?php echo number_format($reseller_info->bank_balance, 2); ?> TK<br>
                            <strong>Drive Balance:</strong> <?php echo number_format($reseller_info->drive_bal, 2); ?> TK
                        </div>
                    </div>
                </div>
            </div>
            
            <?php echo form_open('admin/directBalanceAdd/'.$this->mdb->passwordChanger('encrypt', $resid), array('class' => 'form-horizontal')); ?>
            
            <div class="form-group">
                <label class="col-md-3 control-label">Balance Type <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <select name="source" class="form-control" required>
                        <option value="">Select Balance Type</option>
                        <option value="main">Main Balance</option>
                        <option value="bank">Bank Balance</option>
                        <option value="drive">Drive Balance</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-md-3 control-label">Amount <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="number" name="amount" class="form-control" step="0.01" min="1" placeholder="Enter amount to add" required>
                    <small class="text-muted">Minimum amount: 1.00 TK</small>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-md-3 control-label">Description <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <textarea name="description" class="form-control" rows="3" placeholder="Enter reason for balance addition" required></textarea>
                    <small class="text-muted">This will be shown in transaction history</small>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-md-3 control-label">Admin PIN <span class="text-danger">*</span></label>
                <div class="col-md-9">
                    <input type="password" name="pincode" class="form-control" placeholder="Enter your admin PIN" required>
                    <small class="text-muted">Enter your admin PIN to confirm this action</small>
                </div>
            </div>
            
            <div class="form-group">
                <div class="col-md-9 col-md-offset-3">
                    <div class="alert alert-warning">
                        <i class="fa fa-warning"></i> <strong>Important:</strong> 
                        This will add balance directly to the reseller's account immediately. 
                        This action cannot be undone and will NOT go through the pending system.
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fa fa-plus"></i> Add Balance Directly
                    </button>
                    <a href="<?php echo base_url('admin/reseller'); ?>" class="btn btn-default btn-lg">
                        <i class="fa fa-arrow-left"></i> Back to Resellers
                    </a>
                </div>
            </div>
            
            <?php echo form_close(); ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation
    $('form').on('submit', function(e) {
        var amount = parseFloat($('input[name="amount"]').val());
        var source = $('select[name="source"]').val();
        var description = $('textarea[name="description"]').val().trim();
        var pincode = $('input[name="pincode"]').val().trim();
        
        if (!source) {
            alert('Please select balance type');
            e.preventDefault();
            return false;
        }
        
        if (amount < 1) {
            alert('Amount must be at least 1.00 TK');
            e.preventDefault();
            return false;
        }
        
        if (!description) {
            alert('Please enter description');
            e.preventDefault();
            return false;
        }
        
        if (!pincode) {
            alert('Please enter admin PIN');
            e.preventDefault();
            return false;
        }
        
        // Confirm action
        var confirmMsg = 'Are you sure you want to add ' + amount + ' TK to ' + source + ' balance?\n\n';
        confirmMsg += 'Reseller: <?php echo $reseller_info->username; ?>\n';
        confirmMsg += 'Description: ' + description + '\n\n';
        confirmMsg += 'This action cannot be undone!';
        
        if (!confirm(confirmMsg)) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-focus on amount field
    $('input[name="amount"]').focus();
});
</script>

<style>
.panel-info {
    border-color: #bce8f1;
}
.panel-info > .panel-heading {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f;
}
.alert-warning {
    border-left: 4px solid #f0ad4e;
}
.btn-lg {
    padding: 10px 20px;
    font-size: 16px;
}
</style>
