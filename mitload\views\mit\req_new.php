<?php

$sql="SELECT * from module where serviceid='$servicesid' and status='1'";
$query = $this->db->query($sql);

					foreach ($query->result() as $row)
					{

						$portid=$row->id;
						$tilt=$row->title;
						$min_amount=$row->min_amount;
						$max_amount=$row->max_amount;

						$min_length=$row->min_length;
						$max_length=$row->max_length;

					}

?>




<div class="mypage">
<?php echo validation_errors(); ?>
<?php echo form_error(); ?>

<?php 

		

		if ($client_typ[$portid] == $servicesid) {
		
		
	?>
		
					<div class="row" style="margin-top:5px;">
		<div class="col-md-12 fleft">	


	<?php echo form_open('' , array('role' => 'form', 'class' => 'inform well', 'style' => 'width:720px;'));?>
	
	<input type="hidden" id="service" name="service" value="<?= $servicesid; ?>" class="form-control"> 
	
<table style="width:40%;">
			  <tr>
			  <td style="width:40%;vertical-align:top;padding-right:20px;">
			  <div style="float:right"><img src="<?= base_url(); ?>assets/img/<?= $servicesid; ?>.png" height="52" width="52" style="padding: 0px 0px 10px 0px; margin-top: 5px;" /> </div>
			  <p class="help-block">Send <?= $tilt ?>  </p>
			  
			  
			  
			  
			   <?php if($servicesid==64 and isset($_GET['drive']) and $_GET['drive']!=1) { ?>
				 
				   <div class="form-group  ">
                                <label class="control-label" for="country">Operator</label><br/>
                                <select class="select form-control" onClick="htmlData('<?= base_url() ?>main/NetSelect', 'number='+pcode.value)" name="pcode" id="pcode" style="width:100% !important;">
								
								<option value="none" >Choice Operator</option>
								
                                                                         <?php
					$sql = "SELECT * FROM `oparetor` where `pcode` is not null order by id asc"; 
                   $query890 = $this->db->query($sql);

					foreach ($query890->result() as $row3)
					{
					
					?>
<option value="<?php echo $row3->pcode; ?>" ><?php echo $row3->title_op; ?></option>
<?php } ?>
                                                                    </select>
                            </div>
							

					  
				 <?php } ?>
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  	   <?php if($servicesid==16384) { ?>
				 
				   <div class="form-group  ">
                                <label class="control-label" for="country">Operator</label><br/>
                                <select class="select form-control" onClick="htmlData('<?= base_url() ?>main/NetSelect', 'number='+pcode.value)" onchange="htmlData('<?= base_url() ?>main/NetSelect', 'number='+pcode.value)" name="pcode" id="pcode" style="width:100% !important;">
								
								<option value="none" >Choice Operator</option>
								
                                                                         <?php
					$sql = "SELECT * FROM `net_op` order by id asc"; 
                   $query890 = $this->db->query($sql);

					foreach ($query890->result() as $row3)
					{
					
					?>
<option value="<?php echo $row3->prefix; ?> " ><?php echo $row3->opname; ?></option>
<?php } ?>
                                                                    </select>
                            </div>
							

					  
				 <?php }else if(isset($_GET['drive']) and $_GET['drive']==1) { ?>
				 
				   <div class="form-group  ">
                                <label class="control-label" for="country">Operator</label><br/>
                                <select class="select form-control" onClick="htmlData('<?= base_url() ?>main/NetSelect', 'number='+pcode.value+'&drive=1')" onchange="htmlData('<?= base_url() ?>main/NetSelect', 'number='+pcode.value+'&drive=1')" name="pcode" id="pcode" style="width:100% !important;">
								
								<option value="none" >Choice Operator</option>
								
                                                                         <?php
					$sql = "SELECT * FROM `net_op` order by id asc"; 
                   $query890 = $this->db->query($sql);

					foreach ($query890->result() as $row3)
					{
					
					?>
<option value="<?php echo $row3->prefix; ?> " ><?php echo $row3->opname; ?></option>
<?php } ?>
                                                                    </select>
                            </div>
							

					  
				 <?php } ?>
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  
			  <?php if($servicesid==16384) { ?>
			  
			  			 <?php 
                        if(form_error('acnumber')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="acnumber">Number</label>
				<input type="text" name="acnumber" id="number" class="form-control input-sm" value="<?=set_value('acnumber')?>" placeholder="eg: 0171XXXXXXX" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Number <?php echo $min_length ?>, Max Number <?php echo $max_length ?> ]</p>
			  </div>
			  
			  
			  
			    <div id="txtResult">
			   </div>
			     <?php if($servicesid==16384) { ?>  <div id="clientResult">
				   <?php 
                        if(form_error('amount')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
			
				
				<input type="hidden" name="amount" id="amount" onKeyPress="return isNumberKey(event)" class="form-control input-sm" value="<?=set_value('amount')?>" placeholder="eg: 100" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Amount <?php echo $min_amount ?>, Max Amount <?php echo $max_amount ?> ]</p>
				
			  </div><?php }else{?> 
			   
			      <div id="clientResult">
				   <?php 
                        if(form_error('amount')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="amount"><?php echo translate('amount'); ?></label>
				
				<input type="text" name="amount" id="amount" onKeyPress="return isNumberKey(event)" class="form-control input-sm" value="<?=set_value('amount')?>" placeholder="eg: 100" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Amount <?php echo $min_amount ?>, Max Amount <?php echo $max_amount ?> ]</p>
				
			  </div>
			  <?php } ?>
			  
				  </div>
				  
				  <?php }else if (isset($_GET['drive']) and $_GET['drive']==1){  ?>
			  
			  			 <?php 
                        if(form_error('acnumber')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="acnumber">Number</label>
				<input type="text" name="acnumber" id="number" class="form-control input-sm" value="<?=set_value('acnumber')?>" placeholder="eg: 0171XXXXXXX" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Number <?php echo $min_length ?>, Max Number <?php echo $max_length ?> ]</p>
			  </div>
			  
			  
			  
			    <div id="txtResult">
			   </div>
			     <?php if($servicesid==16384) { ?>  <div id="clientResult">
				   <?php 
                        if(form_error('amount')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
			
				
				<input type="hidden" name="amount" id="amount" onKeyPress="return isNumberKey(event)" class="form-control input-sm" value="<?=set_value('amount')?>" placeholder="eg: 100" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Amount <?php echo $min_amount ?>, Max Amount <?php echo $max_amount ?> ]</p>
				
			  </div><?php }else{?> 
			   
			      <div id="clientResult">
				   <?php 
                        if(form_error('amount')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="amount"><?php echo translate('amount'); ?></label>
				
				<input type="text" name="amount" id="amount" onKeyPress="return isNumberKey(event)" class="form-control input-sm" value="<?=set_value('amount')?>" placeholder="eg: 100" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Amount <?php echo $min_amount ?>, Max Amount <?php echo $max_amount ?> ]</p>
				
			  </div>
			  <?php } ?>
			  
				  </div>
				  
				  <?php } else { ?>
				  
				 <?php if($servicesid==512) { ?>
				 
				   <div class="form-group  ">
                                <label class="control-label" for="country">Country</label><br/>
                                <select class="select form-control" name="country" id="country" style="width:100% !important;" onChange="htmlData('<?= base_url() ?>/errorsite/getoperators', 'country='+country.value)">
								
								<option value="none" >Choice Country</option>
								
                                                                         <?php
					$sql = "SELECT * FROM `country` order by id asc"; 
                   $query89 = $this->db->query($sql);

					foreach ($query89->result() as $row)
					{
					
					?>
<option value="<?php echo $row->id; ?> " ><?php echo $row->country_name; ?></option>
<?php } ?>
                                                                    </select>
                            </div>
							<div id="txtResult">
							 </div>

					  
				 <?php } ?>
				  
				  
				   <?php if($servicesid!=512) { ?>
				   <?php 
                        if(form_error('acnumber')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="acnumber">Number</label>
				<input type="text" name="acnumber" id="number" onKeyPress="return isNumberKey(event)" class="form-control input-sm" value="<?=set_value('acnumber')?>" placeholder="eg: 0171XXXXXXX" required>
				<?php echo form_error(); ?>
				<p class="help-block form_note">[ Min Number <?php echo $min_length ?>, Max Number <?php echo $max_length ?> ]</p>
			  </div>
				   <?php } ?>
			  
			 <?php 
                        if(form_error('amount')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                 if($servicesid==65536) {   ?>
                    
                    	<label class="control-label" for="amount"><?php echo translate('amount'); ?></label>
					<select class="form-control input-sm" name="amount" id="amount">
								 
                                         <option value="50">50 BDT</option>
                                          <option value="100">100 BDT</option>
                                          <option value="200">200 BDT</option>
                                           <option value="500">500 BDT</option>
                                            <option value="1000">1000 BDT</option>
                             </select>    
                    <?php } else{?>
                    
                    
				<label class="control-label" for="amount"><?php echo translate('amount'); ?></label>
				
				<input type="text" name="amount" id="amount" onKeyPress="return isNumberKey(event)" class="form-control input-sm" value="<?=set_value('amount')?>" placeholder="eg: 100" required>
				
				
				
				
				<?php } echo form_error(); ?>
				<p class="help-block form_note">[ Min Amount <?php echo $min_amount ?>, Max Amount <?php echo $max_amount ?> ]</p>
				
			  </div>
			  
				  <?php } ?>
			  
			  
			 <?php 
                        if(form_error('type')) 
                            echo "<div class='form-group has-error' >";
                        else     
                            echo "<div class='form-group' >";
                    ?>
				<label class="control-label" for="type"><?php echo translate('type'); ?></label>
				
				 <?php if($servicesid!=524288 ) { ?>
				<select class="form-control input-sm" name="type" id="type">
								 <?php if($servicesid==64 or $servicesid==512 or $servicesid==16384 or $servicesid==65536) { ?>
                                         <option value="1"><?php echo translate('prepaid'); ?></option>
                                         <option value="2"><?php echo translate('postpaid'); ?></option>
										  <?php }
									  else { ?>
										<option value="1"><?php echo translate('Cash IN'); ?></option>
                                         <option value="2"><?php echo translate('Cashout'); ?></option>
                                          <option value="3"><?php echo translate('Send Money'); ?></option>
                                          <option value="4"><?php echo translate('B2B'); ?></option>
                                         
											
										  <?php } ?>
				  				</select>
				  				
				  				
				  		 <?php } if($servicesid==524288 ) { ?>	
			 <select class="select form-control" onchange="bilf()" name="type" id="type" style="width:100% !important;">
								
                                       	<option value="1">Pay many time </option>
                                         <option value="2">Pay First time</option>
										  
									
											
										
				  				</select>
		  <?php } ?>
				  				
				 
				  				
				  				
			  </div>
			  <div id="hidebill" style="display: none;">
			  
			  <div class='form-group' >
			  
			  <label class="control-label" for="name"><?php echo translate('name'); ?></label>
				
				<input type="text" name="name" id="name" class="form-control input-sm" value="<?=set_value('name')?>" placeholder="eg: Kamrul khan"></div>
				<div class='form-group' >
					<label class="control-label" for="mobile"><?php echo translate('mobile'); ?></label>
				
				<input type="text" name="mobile" id="mobile" class="form-control input-sm" value="<?=set_value('mobile')?>" placeholder="eg: 01xxxxxxxxx">
				</div>
			  </div>
			  <p class="help-block form_error" style="font-size:11px;"></p>
			  <p class="help-block line">&nbsp;</p>
			  <button type="submit" class="btn btn-primary btn-sm" ><span class="glyphicon glyphicon-send"></span> &nbsp;<?php echo translate('send'); ?></button>
			  </td></tr><tr>
			
			  <td style="width:40%;vertical-align:top;padding-right:15px;">
			  <p class="help-block">Last 10 Requests</p>
				<div style="margin:0px;padding:0px;background:#fff;">
				<table cellspacing="0" class="table10">
					<thead>
						<tr>	
						<th>Number</th>
						<th>Amount</th>
						<th>Cost</th>
						<th>Trnx</th>
						<th>Status</th>
						</tr>
					</thead>
					<tbody>
					
					

						
					<?php
$query1="select * from sendflexi where userid='$uid' and service='$servicesid' order by id desc limit 10"; 



$query = $this->db->query($query1);
if(!empty($query->result())) {

					foreach ($query->result() as $row)
					{
						
$slid=$row->id;
$phone=$row->phone;
$balance=$row->balance;
$cost=$row->cost;
$type=$row->type;
$flxstatus=$row->status;
$flxlocal=$row->local; 
 $trnx=$row->trxid;

$trnx = substr($trnx, 0, 5); 

if($flxstatus==0 && $flxlocal==0){$st="<font color=#DE00FF><b>Pending</b></font>";} 
if($flxstatus==1 or $flxlocal==1){$st="<font color=#60C36A><b>Success</b></font></a>";} 
if($flxstatus==3){$st="<font color=#FF0000><b>Cancelled</b></font>";} 
if($flxstatus==4){$st="<font color=blue><b>Processing</b></font></a>";} 
if($flxstatus==5){$st="<font color=red><b>Waiting</b></font></a>";} 
if($flxstatus==2){$st="<font color=black><b>Faild</b></font></a>";} 



if($servicesid=='16' or $servicesid=='64' or $servicesid=='512' or $servicesid=='16384' or $servicesid=='65536') { 
if($type==1) {$sttypmsg="Prepaid";} else {$sttypmsg="PostPaid";}

}else {
  if($type==1) {$sttypmsg="Personal";} else {$sttypmsg="Agent";}
  }
  
  if($flxstatus==1) {$trcolor="#DFF0D8";}
  if($flxstatus==0) {$trcolor="#EBCCCC";}
   if($flxstatus==4) {$trcolor="#D9EDF7";}
   if($flxstatus==5) {$trcolor="";}

 
?>



						
						<tr bgcolor="<?php echo $trcolor; ?>">
						<td><?php echo $phone; ?></td>
						<td><?php if($servicesid==16384) { echo $cost; } else{echo $balance; } ?></td>
						<td><?php echo $cost; ?></td>
						
						<td><?php echo $trnx; ?></td>
						<td style="font-weight:bold;"><?php echo $st; ?></td>
					</tr>
					
					
<?php } }else {?>

<tr>
<td colspan="5"><?php echo "No Requests Found.";  ?></td>
					</tr>
<?php } ?>
					
					
															</tbody>
				</table>
				</td>
			  </tr>
			</table>

                            </form>
                       
				
					<?php }
			 else {
			
			echo "not allow";
			
			}
			
			?>
					
                </div>
			
	<script>
function bilf() {
  var x = document.getElementById("type").value;
  
  
   if(x==1){
    document.getElementById('hidebill').style.display = "none";
   } else{
    document.getElementById('hidebill').style.display = "block";
   }
  
}
</script>
